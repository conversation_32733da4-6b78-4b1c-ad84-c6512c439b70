package top.lacrus.lootingsystem.block.entity;

import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.entity.ChestBlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.ChestType;
import top.lacrus.lootingsystem.block.custom.SafeBlock;

public class SafeBlockEntity extends ChestBlockEntity {

    public SafeBlockEntity(BlockPos pos, BlockState blockState) {
        super(ModBlockEntities.SAFE_BLOCK_ENTITY.get(), pos, blockState);
    }

    @Override
    protected Component getDefaultName() {
        return Component.translatable("container.lootingsystem.safe");
    }

    @Override
    protected AbstractContainerMenu createMenu(int id, Inventory inventory) {
        return ChestMenu.threeRows(id, inventory, this);
    }

    protected void playSound(BlockState state, SoundEvent sound) {
        ChestType chesttype = state.getValue(SafeBlock.TYPE);
        if (chesttype != ChestType.LEFT) {
            double d0 = (double)this.worldPosition.getX() + 0.5D;
            double d1 = (double)this.worldPosition.getY() + 0.5D;
            double d2 = (double)this.worldPosition.getZ() + 0.5D;
            if (chesttype == ChestType.RIGHT) {
                Direction direction = SafeBlock.getDirectionToAttached(state);
                d0 += (double)direction.getStepX() * 0.5D;
                d2 += (double)direction.getStepZ() * 0.5D;
            }

            this.level.playSound((Player)null, d0, d1, d2, sound, SoundSource.BLOCKS, 0.5F, this.level.random.nextFloat() * 0.1F + 0.9F);
        }
    }

    protected SoundEvent getOpenSound() {
        return SoundEvents.CHEST_OPEN;
    }

    protected SoundEvent getCloseSound() {
        return SoundEvents.CHEST_CLOSE;
    }

    public static void lidAnimateTick(Level level, BlockPos pos, BlockState state, SafeBlockEntity blockEntity) {
        // Access the lid controller through reflection or use a different approach
        // For now, we'll use the parent class method
        ChestBlockEntity.lidAnimateTick(level, pos, state, blockEntity);
    }
}
